<template>
  <div class="search-panel">
    <a-card>
      <div class="search-line">
        <!-- <div class="search-item">
          <div class="label">场馆</div>
          <BusSelectAdmin v-model="searchParams.bus_id" class="value" placeholder="请选择场馆" />
        </div> -->
        <div class="search-item">
          <div class="label">美团核销码</div>
          <a-input v-model="searchParams.subscription_no" class="value" placeholder="请输入美团核销码" allow-clear />
        </div>
        <div class="search-item">
          <div class="label">商品名称</div>
          <a-input v-model="searchParams.subscription_no" class="value" placeholder="请输入商品名称" allow-clear />
        </div>
        <div class="search-item">
          <div class="label">会员姓名</div>
          <a-input v-model="searchParams.subscription_no" class="value" placeholder="请输入会员姓名" allow-clear />
        </div>
        <div class="search-item">
          <div class="label">会员联系方式</div>
          <a-input v-model="searchParams.subscription_no" class="value" placeholder="请输入会员联系方式" allow-clear />
        </div>
        <div class="search-item">
          <div class="label">合约编号</div>
          <a-input v-model="searchParams.subscription_no" class="value" placeholder="请输入合约编号" allow-clear />
        </div>
        <!-- <div v-if="category === tabList[0]" class="search-item">
          <div class="label">合约状态</div>
          <a-select v-model="searchParams.status" class="value" placeholder="请选择合约状态" allow-clear>
            <a-option v-for="item in contractStatusList" :key="item.value" :value="item.value">
              {{ item.label }}
            </a-option>
          </a-select>
        </div> -->
        <!-- <div class="search-item">
          <div class="label">产品方案</div>
          <a-input v-model="searchParams.product_title" class="value" placeholder="请输入月付方案名称" allow-clear />
        </div> -->
        <!-- <div class="search-item">
          <div class="label">签约会员</div>
          <a-input v-model="searchParams.search" class="value" placeholder="请输入会员姓名/手机号码" allow-clear />
        </div> -->
        <div class="search-item">
          <div class="label">核销时间</div>
          <a-range-picker v-model="daterange" style="width: 300px; margin-left: 10px" format="YYYY-MM-DD" />
        </div>
        <!-- <div v-if="tabList.slice(2, 5).includes(category)" class="search-item">
          <div class="label">{{ dateLabel }}</div>
          <a-range-picker v-model="otherDaterange" class="value" format="YYYY-MM-DD" />
        </div> -->
        <div class="search-item">
          <a-button type="primary" @click="handleSearch">查询</a-button>
        </div>
        <div class="search-item">
          <a-button @click="handleReset">重置</a-button>
        </div>
      </div>
    </a-card>

    <a-card style="margin-top: 20px">
      <!-- :row-selection="rowSelectionConfig" -->
      <a-table
        v-model:selected-keys="selectedKeys"
        row-key="subscription_no"
        :data="list"
        :pagination="tablePagination"
        @page-change="handlePageChange"
        @page-size-change="handlePageSizeChange">
        <template #columns>
          <a-table-column title="合约编号" data-index="subscription_no">
            <template #cell="{ record }">
              <a-link @click="openDetail(record)">{{ record.subscription_no }}</a-link>
            </template>
          </a-table-column>
          <a-table-column title="商品名称" data-index="product_title" />
          <a-table-column title="美团跑路赔">
            <template #cell="{ record }">
              <span>{{ record.product_type === 2 ? '组合' : '常规' }}</span>
            </template>
          </a-table-column>
          <a-table-column title="签约金额(￥)">
            <template #cell="{ record }">{{ toFixed2(record.first_pay_amount) }}</template>
          </a-table-column>
          <!-- <a-table-column title="签约金额">
            <template #cell="{ record }">{{ toFixed2(record.sign_amount) }}</template>
          </a-table-column>
          <a-table-column title="首期金额">
            <template #cell="{ record }">{{ toFixed2(record.down_payment) }}</template>
          </a-table-column>
          <a-table-column title="单期金额">
            <template #cell="{ record }">{{ toFixed2(record.deduction_amount) }}</template>
          </a-table-column> -->
          <a-table-column title="美团核销码" data-index="username" />
          <a-table-column title="会员" data-index="username" />
          <a-table-column title="会员联系方式" data-index="phone" />
          <!-- <a-table-column title="场馆" data-index="bus_name" /> -->
          <a-table-column title="签约销售" data-index="sale_name" />
          <a-table-column title="核销时间(签约)" data-index="sign_time" />
          <a-table-column title="成功扣款期数">
            <template #cell="{ record }">{{ record.paid_count }}/{{ record.periods }}</template>
          </a-table-column>
          <!-- <a-table-column v-if="category === tabList[2]" title="合约暂停时间" data-index="paused_time" />
          <a-table-column v-if="category === tabList[3]" title="解约时间" data-index="surrender_time" />
          <a-table-column v-if="category === tabList[3]" title="违约金金额">
            <template #cell="{ record }">{{ toFixed2(record.violate_amount) }}</template>
          </a-table-column>
          <a-table-column v-if="category === tabList[3]" title="实付违约金">
            <template #cell="{ record }">{{ toFixed2(record.paid_violate_amount) }}</template>
          </a-table-column>
          <a-table-column v-if="category === tabList[4]" title="履约完成时间" data-index="end_times" /> -->
          <a-table-column title="合约状态">
            <template #cell="{ record }">
              <span :style="{ color: contractStatusList.find((i) => i.value === record.status)?.color || '#333' }">
                {{ contractStatusList.find((i) => i.value === record.status)?.text || '-' }}
              </span>
            </template>
          </a-table-column>
          <!-- <a-table-column v-if="category === tabList[5]" title="预计解约时间" data-index="next_plan_deduction_time" /> -->
          <a-table-column title="操作">
            <template #cell="{ record }">
              <!-- <a-dropdown @select="(name) => handleAction(String(name), record)">
                <a-button type="outline">
                  更多操作
                  <icon-down style="margin-left: 8px" />
                </a-button>
                <template #content>
                  <a-doption value="detail">合约详情</a-doption>
                  <a-doption value="deduction">履约扣款记录</a-doption>
                  <a-doption value="pause" :disabled="record.status !== 'NORMAL' || record.delay_surrender == 1">
                    暂停
                  </a-doption>
                  <a-doption value="suspend" :disabled="!(record.status === 'NORMAL' || record.delay_surrender == 1)">
                    解约
                  </a-doption>
                  <a-doption value="recover" :disabled="record.status !== 'PAUSED'">恢复</a-doption>
                  <a-doption value="cancel" :disabled="record.delay_surrender != 1">取消解约</a-doption>
                </template>
              </a-dropdown> -->
              <a-button type="text" @click="openDetail(record)">查看</a-button>
            </template>
          </a-table-column>
        </template>
      </a-table>
      <!-- <div style="margin-top: 10px; display: flex; justify-content: space-between">
        <div>
          <a-button v-if="category === tabList[2]" type="primary" @click="() => handleRecover()">批量恢复</a-button>
          <a-button style="margin-left: 10px" @click="handleExport">导出</a-button>
        </div>
      </div> -->
    </a-card>

    <TheDetailModal v-model:showModal="detailModal" :params="detailParams" />
    <ZhimaSurrenderModal
      v-model="showSurrenderType"
      :info="curInfo"
      :has-next-plan="hasNextPlan"
      @on-success="getList" />
    <TheTerminateWithMoney
      v-model="showTerminateWithMoney"
      :info="curInfo"
      :has-next-plan="hasNextPlan"
      @on-success="getList" />
  </div>
</template>

<script lang="ts" setup>
  import { useRouter, useRoute } from 'vue-router';
  // import { Message, Modal } from '@arco-design/web-vue';
  import { useBusInfoStore } from '@/store';
  import {
    getContracts,
    // subscriptionPause,
    // subscriptionRegain,
    // subscriptionSurrenderCancel,
    // getSubscriptionByZfb,
  } from '@/api/meituan';
  // import BusSelectAdmin from '@/components/bus-select/admin.vue';
  import TheDetailModal from './TheDetailModal.vue';
  import ZhimaSurrenderModal from './ZhimaSurrenderModal.vue';
  import TheTerminateWithMoney from './TheTerminateWithMoney.vue';

  const NONE_SEARCH_PARAMS = {
    bus_id: '',
    subscription_no: '',
    product_title: '',
    search: '',
    status: '',
    marketers_id: '',
    sign_time_begin: '',
    sign_time_end: '',
    time_begin: '',
    time_end: '',
    delay_surrender: 0,
    page_no: 1,
    page_size: 10,
  };

  const props = defineProps<{ category: string; tabList: string[]; lazy?: boolean; tabName?: string }>();
  const emit = defineEmits(['emitCount']);

  const daterange = ref<string[] | any[]>([]);
  const otherDaterange = ref<string[] | any[]>([]);
  const searchParams = reactive({ ...NONE_SEARCH_PARAMS });
  const list = ref<any[]>([]);
  const total = ref<number | string>(0);
  const contractStatusList = [
    { value: 'NORMAL', label: '正常', color: 'green', text: '履约中' },
    // { value: 'PAUSED', label: '暂停', color: 'orange', text: '已暂停' },
    { value: 'SURRENDER', label: '解约', color: 'red', text: '已解约' },
    { value: 'END', label: '完成', color: 'blue', text: '已完成' },
  ];
  const selectedKeys = ref<any[]>([]);
  const detailModal = ref(false);
  const detailParams = reactive({ bus_id: '', subscription_no: '', card_type: '' as any });
  const showSurrenderType = ref(false);
  const curInfo = ref<any>({});
  const isLeave = ref(false);
  const showTerminateWithMoney = ref(false);
  const hasNextPlan = ref(false);

  // const dateLabel = computed(() => {
  //   if (props.category === props.tabList[2]) return '合约暂停时间';
  //   if (props.category === props.tabList[3]) return '解约时间';
  //   if (props.category === props.tabList[4]) return '履约完成时间';
  //   return '时间';
  // });

  // const router = useRouter();
  const route = useRoute();

  const tablePagination = computed(() => ({
    current: searchParams.page_no,
    pageSize: searchParams.page_size,
    total: total.value,
    showTotal: true,
    showPageSize: true,
  })) as any;

  // const rowSelectionConfig = computed(() => {
  //   const need = [props.tabList[1], props.tabList[2], props.tabList[5]].includes(props.category);
  //   return need ? { type: 'checkbox' as const, showCheckedAll: true, onlyCurrent: false } : undefined;
  // });

  function toFixed2(v: any) {
    const n = Number(v || 0);
    return n.toFixed(2);
  }

  function formatDate(d: any) {
    const day = typeof d === 'string' ? new Date(d) : d;
    const y = day.getFullYear();
    const m = `${day.getMonth() + 1}`.padStart(2, '0');
    const dd = `${day.getDate()}`.padStart(2, '0');
    return `${y}-${m}-${dd}`;
  }

  async function getList() {
    showSurrenderType.value = false;
    const { data } = await getContracts().execute({ data: searchParams });
    const value = data.value as any;
    list.value = value?.list || [];
    total.value = value?.count || 0;
    emit('emitCount', total.value, props.category);
  }

  function handleSearch() {
    searchParams.page_no = 1;
    if (Array.isArray(daterange.value) && daterange.value.length === 2 && daterange.value[0] && daterange.value[1]) {
      searchParams.sign_time_begin = formatDate(daterange.value[0]);
      searchParams.sign_time_end = formatDate(daterange.value[1]);
    } else {
      searchParams.sign_time_begin = '';
      searchParams.sign_time_end = '';
    }
    if (
      Array.isArray(otherDaterange.value) &&
      otherDaterange.value.length === 2 &&
      otherDaterange.value[0] &&
      otherDaterange.value[1]
    ) {
      searchParams.time_begin = formatDate(otherDaterange.value[0]);
      searchParams.time_end = formatDate(otherDaterange.value[1]);
    } else {
      searchParams.time_begin = '';
      searchParams.time_end = '';
    }
    getList();
  }

  function postCategory() {
    if (props.category === props.tabList[0]) {
      searchParams.status = '';
      searchParams.delay_surrender = 0;
    } else if (props.category === props.tabList[5]) {
      searchParams.status = '';
      searchParams.delay_surrender = 1;
    } else {
      searchParams.status = props.category;
      searchParams.delay_surrender = 0;
    }
  }

  function handlePageChange(page: number) {
    searchParams.page_no = page;
    getList();
  }
  function handlePageSizeChange(pageSize: number) {
    searchParams.page_size = pageSize;
    getList();
  }

  // async function handleExport() {
  //   const params = { ...searchParams, is_export: 1, page_no: 1, page_size: total.value };
  //   await getContracts().execute({ data: params });
  //   Message.success({ content: '导出任务运行中，请稍后到消息中心下载!', duration: 3000 });
  // }

  // function modalConfirm({ content, onOk }: { content: string; onOk: () => Promise<void> | void }) {
  //   Modal.confirm({ title: '提示', content, onOk });
  // }

  // function postAction({ openMerchantId, subscriptionNo, cardType, url, text, content }: any) {
  //   // 支持批量：当未指定参数时，从选中行中组装
  //   if (!openMerchantId || !subscriptionNo) {
  //     const keys = selectedKeys.value;
  //     if (!keys.length) {
  //       Message.warning(text);
  //       return;
  //     }
  //     const selectedRows = list.value.filter((r) => keys.includes(r.subscription_no));
  //     if (!selectedRows.length) {
  //       Message.warning(text);
  //       return;
  //     }
  //     openMerchantId = selectedRows[0].open_merchant_id;
  //     subscriptionNo = selectedRows.map((r) => r.subscription_no).join(',');
  //     // cardType 可选
  //   }
  //   modalConfirm({
  //     content,
  //     onOk: async () => {
  //       const apiMap: any = {
  //         '/Web/BusinessFit/subscriptionPause': subscriptionPause,
  //         '/Web/BusinessFit/subscriptionRegain': subscriptionRegain,
  //         '/Web/BusinessFit/subscriptionSurrenderCancel': subscriptionSurrenderCancel,
  //       };
  //       const api = apiMap[url];
  //       if (!api) return;
  //       await api().execute({
  //         data: {
  //           bus_id: searchParams.bus_id,
  //           subscription_no: subscriptionNo,
  //           card_type: cardType,
  //           open_merchant_id: openMerchantId,
  //         },
  //       });
  //       Message.success('操作成功');
  //       getList();
  //     },
  //   });
  // }

  // function handlePause(openMerchantId?: string, subscriptionNo?: string, cardType?: string) {
  //   postAction({
  //     openMerchantId,
  //     subscriptionNo,
  //     cardType,
  //     url: '/Web/BusinessFit/subscriptionPause',
  //     text: '请勾选要暂停的合约',
  //     content: '确认暂停该合约?',
  //   });
  // }
  // function handleRecover(openMerchantId?: string, subscriptionNo?: string, cardType?: string) {
  //   postAction({
  //     openMerchantId,
  //     subscriptionNo,
  //     cardType,
  //     url: '/Web/BusinessFit/subscriptionRegain',
  //     text: '请勾选要恢复的合约',
  //     content: '确认恢复该合约?',
  //   });
  // }
  // function handleCancel(openMerchantId?: string, subscriptionNo?: string, cardType?: string) {
  //   postAction({
  //     openMerchantId,
  //     subscriptionNo,
  //     cardType,
  //     url: '/Web/BusinessFit/subscriptionSurrenderCancel',
  //     text: '请勾选要取消解约的合约',
  //     content: '确认取消解约该合约?',
  //   });
  // }

  // async function handleAction(action: string, row: any) {
  //   const disabledMap: any = {
  //     pause: row.status !== 'NORMAL' || Number(row.delay_surrender) === 1,
  //     suspend: !(row.status === 'NORMAL' || Number(row.delay_surrender) === 1),
  //     recover: row.status !== 'PAUSED',
  //     cancel: Number(row.delay_surrender) !== 1,
  //   };
  //   if (disabledMap[action]) return;

  //   if (action === 'detail') {
  //     detailParams.bus_id = searchParams.bus_id;
  //     detailParams.subscription_no = row.subscription_no;
  //     detailParams.card_type = row.card_type;
  //     detailModal.value = true;
  //   } else if (action === 'deduction') {
  //     router.push({
  //       path: '/Web/BusinessFit/fitItemsList',
  //       query: { busId: searchParams.bus_id, subscriptionNo: row.subscription_no },
  //     });
  //   } else if (action === 'pause') {
  //     handlePause(row.open_merchant_id, row.subscription_no, row.card_type);
  //   } else if (action === 'suspend') {
  //     if (Number(row.delay_surrender) === 1 && row.card_type !== 'AXF_MERCHANT_PERIOD_PAY') {
  //       postAction({
  //         openMerchantId: row.open_merchant_id,
  //         subscriptionNo: row.subscription_no,
  //         cardType: row.card_type,
  //         url: '/Web/BusinessFit/subscriptionSurrender',
  //         text: '请勾选要解约的合约',
  //         content: '确认立即解约该合约?',
  //       });
  //     } else {
  //       const { data } = await getSubscriptionByZfb().execute({
  //         data: { subscription_no: row.subscription_no, scene: 'cancel' },
  //       });
  //       const damage = data.value;
  //       if (!damage) return;
  //       hasNextPlan.value = Number(damage.delay_surrender) !== 1;
  //       if (damage.damages_rate) {
  //         curInfo.value = {
  //           ...damage,
  //           bus_id: searchParams.bus_id,
  //           open_merchant_id: row.open_merchant_id,
  //           subscription_no: row.subscription_no,
  //           done_period: row.done_period,
  //           periods: row.periods,
  //           card_type: row.card_type,
  //           un_sign_type: row.un_sign_type,
  //         };
  //         showTerminateWithMoney.value = true;
  //       } else {
  //         curInfo.value = row;
  //         showSurrenderType.value = true;
  //       }
  //     }
  //   } else if (action === 'recover') {
  //     handleRecover(row.open_merchant_id, row.subscription_no, row.card_type);
  //   } else if (action === 'cancel') {
  //     handleCancel(row.open_merchant_id, row.subscription_no, row.card_type);
  //   }
  // }

  function openDetail(record: any) {
    detailParams.bus_id = searchParams.bus_id;
    detailParams.subscription_no = record.subscription_no;
    detailParams.card_type = record.card_type;
    detailModal.value = true;
  }

  function handleReset() {
    daterange.value = [];
    otherDaterange.value = [];
    Object.assign(searchParams, { ...NONE_SEARCH_PARAMS });
    searchParams.bus_id = (route.query.busId as string) || '';
    postCategory();
    handleSearch();
  }

  onMounted(async () => {
    const lazyTime = props.lazy ? 1000 : 0;
    const busStore = useBusInfoStore();
    setTimeout(async () => {
      searchParams.bus_id = (route.query.busId as string) || (busStore.busInfo.bus_id as any) || '';
      const reload = props.category === (route.query.category as string);
      if (!reload) {
        postCategory();
        getList();
      }
    }, lazyTime);
  });

  onActivated(() => {
    isLeave.value = false;
    if (Number(route?.query?.refresh) === 1) {
      getList();
    }
    const { busId, date, category } = route.query as any;
    const reload = props.category === category;
    if (reload) {
      daterange.value = [];
      otherDaterange.value = [];
      Object.assign(searchParams, { ...NONE_SEARCH_PARAMS });

      searchParams.bus_id = busId;
      const dateObj = new Date(date);
      const firstDate = new Date(dateObj.getFullYear(), dateObj.getMonth(), 1);
      const lastDate = new Date(dateObj.getFullYear(), dateObj.getMonth() + 1, 0);
      daterange.value = [firstDate, lastDate];
      setTimeout(() => {
        postCategory();
        handleSearch();
      }, 1000);
    } else {
      setTimeout(() => {
        if (props.tabName === props.category) {
          postCategory();
          handleSearch();
        }
      }, 1000);
    }
  });
</script>

<style lang="less" scoped>
  .wrap-line {
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    align-items: center;
  }

  .search-panel {
    // padding: 20px;

    .search-line {
      .wrap-line;

      .search-item {
        .wrap-line;
        margin: 10px;

        .value {
          margin-left: 10px;
          width: 200px;
        }
      }
    }
  }
</style>
