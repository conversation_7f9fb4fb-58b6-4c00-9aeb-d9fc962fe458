import { DEFAULT_LAYOUT } from '../base';
import { AppRouteRecordRaw } from '../types';

const ALIPAY: AppRouteRecordRaw = {
  path: '/meituan',
  name: '<PERSON><PERSON><PERSON>',
  meta: {
    locale: '美团跑路赔',
  },
  component: DEFAULT_LAYOUT,
  children: [
    {
      path: 'list',
      name: 'List',
      component: () => import('@/views/meituan/list.vue'),
      meta: {
        keepAlive: true,
        locale: '跑路赔产品管理',
      },
    },
    {
      path: 'monthly',
      name: 'MonthlyPayment',
      component: () => import('@/views/meituan/monthlyPayment.vue'),
      meta: {
        keepAlive: true,
        locale: '合约管理',
      },
    },
    {
      path: 'record',
      name: 'Record',
      component: () => import('@/views/meituan/deductionRecord.vue'),
      meta: {
        keepAlive: true,
        locale: '履约扣款记录',
      },
    },
    {
      path: 'finance',
      name: 'Finance',
      component: () => import('@/views/meituan/financeStatistics.vue'),
      meta: {
        keepAlive: true,
        locale: '财务营收统计',
      },
    },
  ],
};

export default ALIPAY;
